{"name": "fish4fun", "version": "1.0.0", "description": "", "scripts": {"start:dev": "npm run dotenv:local run-p start:dev:ui start:dev:server localenv:launchLocalTunnels", "start:prod:local": "npm run dotenv:local run-p start:ui:prod start:server:prod localenv:launchLocalTunnels", "start:dev:ui": "webpack serve  --mode development --config ./src/app/webpack.config.js", "start:dev:server": "npx nodemon ./src/server/index.js", "start:ui:prod": "webpack  --mode production --config ./src/app/webpack.config.js && serve -s public", "start:server:prod": "npm run convert:backend && node ./dist/server/index.cjs", "convert:backend": "babel ./src -d ./dist  --ignore ./src/app  --out-file-extension .cjs --config-file ./src/babelrc.backend.cjs", "localenv:setup": "npm run dotenv:local node localenv/localEnvSetup/firstTimeLocalSetup.js", "localenv:resetDB": "npm run dotenv:local node localenv/localEnvSetup/resetLocalDB.js", "localenv:launchLocalTunnels": "node localenv/launchlocalTunnels.js", "dotenv:local": "dotenv -e ./env/configs/.env -e ./env/configs/local.env", "dotenv:dev": "dotenv -e ./env/configs/.env -e ./env/configs/dev.env", "dotenv:prod": "dotenv -e ./env/configs/.env -e ./env/configs/prod.env", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf public node_modules/.cache", "clean-build:backend": "rm -rf dist", "clean-build:client": "rm -rf public", "format": "prettier --write ./src/app"}, "author": "M.A.D. Computer Consulting LLC", "license": "ISC", "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^6.1.6", "@mui/material": "^6.1.6", "@napi-rs/canvas": "^0.1.44", "@paypal/react-paypal-js": "^8.3.0", "acme-http-01-webroot": "^3.0.0", "axios": "^1.9.0", "body-parser": "^1.20.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cross-env": "^7.0.3", "devcert": "^1.2.2", "discord.js": "^14.14.1", "dotenv": "^16.3.1", "ejs": "^3.1.9", "ethers": "^6.8.1", "express": "^4.18.2", "greenlock": "^4.0.4", "html-webpack-plugin": "^5.5.3", "localtunnel": "^2.0.2", "mongodb-memory-server": "^10.1.4", "mongoose": "^8.16.0", "multer": "^1.4.5-lts.1", "nodemon": "^3.1.7", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.23.1", "uuid": "^11.0.2", "web-vitals": "^4.2.4", "zustand": "^5.0.3"}, "devDependencies": {"@babel/cli": "^7.22.10", "@babel/core": "^7.23.9", "@babel/plugin-transform-class-properties": "^7.23.3", "@babel/preset-env": "^7.23.5", "@babel/preset-react": "^7.23.3", "babel-loader": "^9.1.3", "babel-plugin-replace-import-extension": "^1.1.5", "babel-preset-react": "^6.24.1", "copy-webpack-plugin": "^13.0.1", "css-loader": "^7.1.2", "dotenv-cli": "^10.0.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.0", "npm-run-all": "^4.1.5", "serve": "^14.2.4", "style-loader": "^4.0.0", "webpack": "^5.90.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0", "webpack-node-externals": "^3.0.0"}, "type": "module"}